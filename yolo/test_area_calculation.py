#!/usr/bin/env python3
"""
测试面积计算功能
"""

def testAreaCalculation():
    """测试面积计算功能"""
    try:
        from predict_seg import WaterSegmentationPredictor
        
        print("🧪 测试面积计算功能")
        print("=" * 50)
        
        # 创建预测器
        predictor = WaterSegmentationPredictor()
        
        # 使用网络图片测试
        test_url = "https://ultralytics.com/images/bus.jpg"
        
        print(f"📸 测试图片: {test_url}")
        print("🔄 开始预测...")
        
        # 执行预测
        results = predictor.predict(
            source=test_url,
            conf=0.4,
            iou=0.5,
            imgsz=640,
            show_labels=True,
            show_conf=True,
            line_width=2,
            save_plots=True
        )
        
        print("✅ 预测完成！")
        print("📁 请查看 predict_result_seg/prediction/ 目录")
        print("💡 文件名应该包含面积百分比信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    testAreaCalculation()
