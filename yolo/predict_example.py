#!/usr/bin/env python3
"""
水体分割预测示例脚本
使用训练好的模型对不同类型的输入进行预测
"""

import os
from pathlib import Path
from predict_seg import WaterSegmentationPredictor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def predictSingleImage():
    """预测单张图片示例"""
    logger.info("=== 单张图片预测示例 ===")
    
    # 创建预测器
    predictor = WaterSegmentationPredictor()
    
    # 预测网络图片
    image_url = "https://ultralytics.com/images/bus.jpg"
    
    try:
        results = predictor.predict(
            source=image_url,
            conf=0.25,  # 置信度阈值
            imgsz=640,  # 图像大小
            save_txt=True,  # 保存标注文件
            save_conf=True  # 保存置信度
        )
        logger.info("单张图片预测完成！")
        return results
    except Exception as e:
        logger.error(f"单张图片预测失败: {e}")
        return None

def predictLocalImage(image_path):
    """预测本地图片示例"""
    logger.info("=== 本地图片预测示例 ===")
    
    if not os.path.exists(image_path):
        logger.warning(f"图片文件不存在: {image_path}")
        return None
    
    # 创建预测器
    predictor = WaterSegmentationPredictor()
    
    try:
        results = predictor.predict(
            source=image_path,
            conf=0.3,   # 稍高的置信度阈值
            imgsz=640,
            save_txt=True,
            save_conf=True,
            save_crop=True  # 保存裁剪的检测区域
        )
        logger.info("本地图片预测完成！")
        return results
    except Exception as e:
        logger.error(f"本地图片预测失败: {e}")
        return None

def predictImageFolder(folder_path):
    """预测图片文件夹示例"""
    logger.info("=== 图片文件夹预测示例 ===")
    
    if not os.path.exists(folder_path):
        logger.warning(f"文件夹不存在: {folder_path}")
        return None
    
    # 创建预测器
    predictor = WaterSegmentationPredictor()
    
    try:
        results = predictor.predict(
            source=folder_path,
            conf=0.25,
            imgsz=640,
            save_txt=True,
            save_conf=True
        )
        logger.info("图片文件夹预测完成！")
        return results
    except Exception as e:
        logger.error(f"图片文件夹预测失败: {e}")
        return None

def predictVideo(video_path):
    """预测视频文件示例"""
    logger.info("=== 视频文件预测示例 ===")
    
    if not os.path.exists(video_path):
        logger.warning(f"视频文件不存在: {video_path}")
        return None
    
    # 创建预测器
    predictor = WaterSegmentationPredictor()
    
    try:
        results = predictor.predict(
            source=video_path,
            conf=0.3,
            imgsz=640,
            save_txt=True,
            save_conf=True
        )
        logger.info("视频文件预测完成！")
        return results
    except Exception as e:
        logger.error(f"视频文件预测失败: {e}")
        return None

def predictWebcam():
    """预测摄像头示例"""
    logger.info("=== 摄像头预测示例 ===")
    
    # 创建预测器
    predictor = WaterSegmentationPredictor()
    
    try:
        # 注意：摄像头预测会持续运行，按 'q' 键退出
        logger.info("开始摄像头预测，按 'q' 键退出...")
        results = predictor.predict(
            source=0,  # 默认摄像头
            conf=0.25,
            imgsz=640,
            save_txt=False,  # 摄像头模式通常不保存txt
            save_conf=False
        )
        logger.info("摄像头预测完成！")
        return results
    except Exception as e:
        logger.error(f"摄像头预测失败: {e}")
        return None

def main():
    """主函数 - 演示不同的预测方式"""
    
    logger.info("开始水体分割预测演示...")
    
    # 1. 预测网络图片（默认示例）
    predictSingleImage()
    
    # 2. 预测本地图片（如果存在）
    local_image = "test_image.jpg"  # 替换为您的图片路径
    if os.path.exists(local_image):
        predictLocalImage(local_image)
    else:
        logger.info(f"跳过本地图片预测，文件不存在: {local_image}")
    
    # 3. 预测图片文件夹（如果存在）
    image_folder = "test_images/"  # 替换为您的图片文件夹路径
    if os.path.exists(image_folder):
        predictImageFolder(image_folder)
    else:
        logger.info(f"跳过文件夹预测，目录不存在: {image_folder}")
    
    # 4. 预测视频文件（如果存在）
    video_file = "test_video.mp4"  # 替换为您的视频文件路径
    if os.path.exists(video_file):
        predictVideo(video_file)
    else:
        logger.info(f"跳过视频预测，文件不存在: {video_file}")
    
    # 5. 摄像头预测（取消注释以启用）
    # logger.info("如需摄像头预测，请取消注释下面的代码")
    # predictWebcam()
    
    logger.info("\n" + "="*60)
    logger.info("预测演示完成！")
    logger.info("预测结果已保存到 'predict_result_seg/prediction' 文件夹中")
    logger.info("="*60)

if __name__ == "__main__":
    main()
