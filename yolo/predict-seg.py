import os
from pathlib import Path
from ultralytics import YOLO
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WaterSegmentationPredictor:
    """水体分割预测器类"""

    def __init__(self, model_path="runs/segment/water_segmentation1/weights/best.pt",
                 output_dir="predict_result_seg"):
        """
        初始化预测器

        Args:
            model_path (str): 训练好的模型路径
            output_dir (str): 预测结果保存目录
        """
        self.model_path = model_path
        self.output_dir = Path(output_dir)
        self.model = None

        # 创建输出目录
        self._createOutputDir()

        # 加载模型
        self._loadModel()

    def _createOutputDir(self):
        """创建输出目录"""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"输出目录已创建: {self.output_dir}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
            raise

    def _loadModel(self):
        """加载训练好的模型"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            self.model = YOLO(self.model_path)
            logger.info(f"模型加载成功: {self.model_path}")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def predict(self, source, conf=0.25, iou=0.7, imgsz=640, save_txt=True,
                save_conf=True, save_crop=False, show_labels=True, show_conf=True):
        """
        执行预测

        Args:
            source (str): 输入源，可以是:
                - 单张图片路径: 'image.jpg'
                - 图片文件夹: 'path/to/images/'
                - 视频文件: 'video.mp4'
                - URL: 'https://example.com/image.jpg'
                - 摄像头: 0 (设备索引)
            conf (float): 置信度阈值 (0.0-1.0)
            iou (float): IoU阈值用于NMS (0.0-1.0)
            imgsz (int): 推理图像大小
            save_txt (bool): 保存检测结果为txt格式
            save_conf (bool): 在txt文件中保存置信度
            save_crop (bool): 保存裁剪的检测图像
            show_labels (bool): 在图像上显示标签
            show_conf (bool): 在图像上显示置信度

        Returns:
            list: 预测结果列表
        """
        try:
            logger.info(f"开始预测，输入源: {source}")

            # 执行预测
            results = self.model.predict(
                source=source,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                save=True,  # 保存预测结果图像
                save_txt=save_txt,  # 保存txt格式结果
                save_conf=save_conf,  # 保存置信度
                save_crop=save_crop,  # 保存裁剪图像
                show_labels=show_labels,  # 显示标签
                show_conf=show_conf,  # 显示置信度
                project=str(self.output_dir),  # 项目目录
                name="prediction",  # 运行名称
                exist_ok=True  # 如果目录存在则覆盖
            )

            logger.info(f"预测完成，结果保存在: {self.output_dir}/prediction")

            # 处理和显示结果信息
            self._processResults(results)

            return results

        except Exception as e:
            logger.error(f"预测过程中出现错误: {e}")
            raise

    def _processResults(self, results):
        """处理和显示预测结果信息"""
        try:
            total_detections = 0

            for i, result in enumerate(results):
                if result.masks is not None:
                    num_masks = len(result.masks.data)
                    total_detections += num_masks

                    logger.info(f"图像 {i+1}: 检测到 {num_masks} 个分割对象")

                    # 获取分割信息
                    if num_masks > 0:
                        # 边界框信息
                        if result.boxes is not None:
                            confidences = result.boxes.conf.cpu().numpy()  # 置信度
                            classes = result.boxes.cls.cpu().numpy()  # 类别

                            for j in range(num_masks):
                                logger.info(f"  对象 {j+1}: 类别={int(classes[j])}, 置信度={confidences[j]:.3f}")

                        # 分割掩码信息
                        masks_data = result.masks.data.cpu().numpy()
                        logger.info(f"  掩码形状: {masks_data.shape}")
                else:
                    logger.info(f"图像 {i+1}: 未检测到分割对象")

            logger.info(f"总计检测到 {total_detections} 个分割对象")

        except Exception as e:
            logger.error(f"处理结果时出现错误: {e}")

def main():
    """主函数"""
    # 创建预测器实例
    predictor = WaterSegmentationPredictor()

    # 示例预测 - 您可以修改这些输入源
    test_sources = [
        # "https://ultralytics.com/images/bus.jpg",  # 网络图片
        # "path/to/your/image.jpg",  # 本地图片
        "/home/<USER>/llm_project/yolo_project/datasets/大坦沙/好氧池面积/project-14-at-2025-08-15-08-36-dff0f5af/val/images/",  # 图片文件夹
        # "path/to/your/video.mp4",  # 视频文件
        # 0,  # 摄像头
    ]

    # 如果没有指定测试源，使用默认的网络图片
    if not test_sources:
        test_sources = ["https://ultralytics.com/images/bus.jpg"]

    # 执行预测
    for source in test_sources:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"正在处理: {source}")
            logger.info(f"{'='*50}")

            predictor.predict(
                source=source,
                conf=0.25,  # 置信度阈值
                iou=0.7,    # IoU阈值
                imgsz=640,  # 图像大小
                save_txt=True,    # 保存txt结果
                save_conf=True,   # 保存置信度
                save_crop=False,  # 不保存裁剪图像
                show_labels=True, # 显示标签
                show_conf=True    # 显示置信度
            )

        except Exception as e:
            logger.error(f"处理 {source} 时出现错误: {e}")
            continue

if __name__ == "__main__":
    main()