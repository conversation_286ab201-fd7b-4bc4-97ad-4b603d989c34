import os
from pathlib import Path
from ultralytics import YOLO
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WaterSegmentationPredictor:
    """水体分割预测器类"""

    def __init__(self, model_path="runs/segment/water_segmentation1/weights/best.pt",
                 output_dir="predict_result_seg"):
        """
        初始化预测器

        Args:
            model_path (str): 训练好的模型路径
            output_dir (str): 预测结果保存目录
        """
        self.model_path = model_path
        self.output_dir = Path(output_dir)
        self.model = None

        # 创建输出目录
        self._createOutputDir()

        # 加载模型
        self._loadModel()

    def _createOutputDir(self):
        """创建输出目录"""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"输出目录已创建: {self.output_dir}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
            raise

    def _loadModel(self):
        """加载训练好的模型"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            self.model = YOLO(self.model_path)
            logger.info(f"模型加载成功: {self.model_path}")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def predict(self, source, conf=0.25, iou=0.7, imgsz=640, show_labels=True,
                show_conf=True, line_width=None, save_plots=True):
        """
        执行预测并将分割结果绘制在图片上

        Args:
            source (str): 输入源，可以是:
                - 单张图片路径: 'image.jpg'
                - 图片文件夹: 'path/to/images/'
                - 视频文件: 'video.mp4'
                - URL: 'https://example.com/image.jpg'
                - 摄像头: 0 (设备索引)
            conf (float): 置信度阈值 (0.0-1.0)
            iou (float): IoU阈值用于NMS (0.0-1.0)
            imgsz (int): 推理图像大小
            show_labels (bool): 在图像上显示标签
            show_conf (bool): 在图像上显示置信度
            line_width (int): 边界框线条宽度，None为自动
            save_plots (bool): 是否保存绘制的结果图像

        Returns:
            list: 预测结果列表
        """
        try:
            logger.info(f"开始预测，输入源: {source}")

            # 执行预测（不自动保存，我们手动处理）
            results = self.model.predict(
                source=source,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                save=False,  # 不自动保存，我们手动绘制和保存
                verbose=True
            )

            logger.info(f"预测完成，开始绘制结果...")

            # 手动绘制和保存结果
            if save_plots:
                self._plotAndSaveResults(results, show_labels, show_conf, line_width)

            # 处理和显示结果信息
            self._processResults(results)

            return results

        except Exception as e:
            logger.error(f"预测过程中出现错误: {e}")
            raise

    def _plotAndSaveResults(self, results, show_labels=True, show_conf=True, line_width=None):
        """
        绘制预测结果并保存到文件

        Args:
            results: 预测结果列表
            show_labels (bool): 是否显示标签
            show_conf (bool): 是否显示置信度
            line_width (int): 线条宽度
        """
        try:
            import cv2

            # 创建保存目录
            save_dir = self.output_dir / "prediction"
            save_dir.mkdir(parents=True, exist_ok=True)

            for i, result in enumerate(results):
                # 计算分割面积占比
                area_percentage = self._calculateAreaPercentage(result)

                # 使用 plot() 方法绘制结果
                plotted_img = result.plot(
                    conf=show_conf,           # 显示置信度
                    labels=show_labels,       # 显示标签
                    boxes=True,               # 显示边界框
                    masks=True,               # 显示分割掩码
                    line_width=line_width,    # 线条宽度
                    font_size=None,           # 字体大小（自动）
                    pil=False                 # 返回numpy数组而不是PIL图像
                )

                # 获取原始图像路径信息
                if hasattr(result, 'path') and result.path:
                    # 从路径中提取文件名
                    original_name = Path(result.path).stem
                    file_extension = Path(result.path).suffix or '.jpg'
                else:
                    # 如果没有路径信息，使用索引命名
                    original_name = f"prediction_{i:04d}"
                    file_extension = '.jpg'

                # 构建保存路径，包含面积百分比信息
                save_path = save_dir / f"{original_name}_segmented_{area_percentage:.2f}%{file_extension}"

                # 转换颜色格式（OpenCV使用BGR，matplotlib使用RGB）
                if plotted_img is not None:
                    # plotted_img 是 RGB 格式的 numpy 数组
                    plotted_img_bgr = cv2.cvtColor(plotted_img, cv2.COLOR_RGB2BGR)

                    # 保存图像
                    success = cv2.imwrite(str(save_path), plotted_img_bgr)

                    if success:
                        logger.info(f"已保存预测结果: {save_path}")
                    else:
                        logger.error(f"保存图像失败: {save_path}")
                else:
                    logger.warning(f"第 {i+1} 张图像的绘制结果为空")

            logger.info(f"所有预测结果已保存到: {save_dir}")

        except ImportError as e:
            logger.error(f"缺少必要的库: {e}")
            logger.error("请安装: pip install opencv-python pillow")
            raise
        except Exception as e:
            logger.error(f"绘制和保存结果时出现错误: {e}")
            raise

    def _calculateAreaPercentage(self, result):
        """
        计算分割面积占图片总面积的百分比

        Args:
            result: 单个预测结果

        Returns:
            float: 面积百分比
        """
        try:
            import numpy as np

            # 获取原始图像尺寸
            orig_height, orig_width = result.orig_shape
            total_image_area = orig_height * orig_width

            if result.masks is not None:
                masks_data = result.masks.data.cpu().numpy()
                total_segmentation_area = 0

                for mask in masks_data:
                    # 将掩码调整到原始图像尺寸
                    if mask.shape != (orig_height, orig_width):
                        import cv2
                        mask_resized = cv2.resize(mask.astype(np.uint8),
                                                (orig_width, orig_height),
                                                interpolation=cv2.INTER_NEAREST)
                        mask = mask_resized.astype(bool)
                    else:
                        mask = mask.astype(bool)

                    # 累加分割面积
                    total_segmentation_area += np.sum(mask)

                # 计算百分比
                area_percentage = (total_segmentation_area / total_image_area) * 100
                return area_percentage
            else:
                return 0.0

        except Exception as e:
            logger.error(f"计算面积百分比时出现错误: {e}")
            return 0.0

    def _processResults(self, results):
        """处理和显示预测结果信息"""
        try:
            import numpy as np
            total_detections = 0

            for i, result in enumerate(results):
                # 获取原始图像尺寸
                orig_height, orig_width = result.orig_shape
                total_image_area = orig_height * orig_width

                logger.info(f"图像 {i+1}: 尺寸 {orig_width}x{orig_height}, 总面积 {total_image_area} 像素")

                if result.masks is not None:
                    num_masks = len(result.masks.data)
                    total_detections += num_masks

                    logger.info(f"  检测到 {num_masks} 个分割对象")

                    # 获取分割信息
                    if num_masks > 0:
                        # 边界框信息
                        if result.boxes is not None:
                            confidences = result.boxes.conf.cpu().numpy()  # 置信度
                            classes = result.boxes.cls.cpu().numpy()  # 类别

                        # 分割掩码信息
                        masks_data = result.masks.data.cpu().numpy()
                        logger.info(f"  掩码形状: {masks_data.shape}")

                        # 计算每个对象的面积和总分割面积
                        total_segmentation_area = 0

                        for j in range(num_masks):
                            # 获取单个掩码
                            mask = masks_data[j]

                            # 将掩码调整到原始图像尺寸
                            if mask.shape != (orig_height, orig_width):
                                import cv2
                                mask_resized = cv2.resize(mask.astype(np.uint8),
                                                        (orig_width, orig_height),
                                                        interpolation=cv2.INTER_NEAREST)
                                mask = mask_resized.astype(bool)
                            else:
                                mask = mask.astype(bool)

                            # 计算当前对象的分割面积
                            object_area = np.sum(mask)
                            total_segmentation_area += object_area

                            # 计算当前对象面积占比
                            area_percentage = (object_area / total_image_area) * 100

                            # 显示详细信息
                            if result.boxes is not None:
                                logger.info(f"    对象 {j+1}: 类别={int(classes[j])}, "
                                          f"置信度={confidences[j]:.3f}, "
                                          f"面积={object_area}像素, "
                                          f"占比={area_percentage:.2f}%")
                            else:
                                logger.info(f"    对象 {j+1}: "
                                          f"面积={object_area}像素, "
                                          f"占比={area_percentage:.2f}%")

                        # 计算总分割面积占比
                        total_area_percentage = (total_segmentation_area / total_image_area) * 100

                        logger.info(f"  📊 总分割面积: {total_segmentation_area} 像素")
                        logger.info(f"  📈 总分割占比: {total_area_percentage:.2f}%")

                else:
                    logger.info(f"  未检测到分割对象")
                    logger.info(f"  📊 总分割面积: 0 像素")
                    logger.info(f"  📈 总分割占比: 0.00%")

                logger.info("-" * 50)

            logger.info(f"🎯 总计检测到 {total_detections} 个分割对象")

        except Exception as e:
            logger.error(f"处理结果时出现错误: {e}")

def main():
    """
    主函数 - 水体分割预测

    使用方法：
    1. 直接运行：python predict-seg.py （使用默认网络图片）
    2. 修改下面的 source 变量来指定您的输入源
    """
    # 创建预测器实例
    predictor = WaterSegmentationPredictor()

    # =================================================================
    # 🔧 在这里修改您的输入源
    # =================================================================

    # 选择您要预测的输入源（取消注释您需要的选项）：

    # 1. 网络图片（默认示例）
    # source = "https://ultralytics.com/images/bus.jpg"

    # 2. 本地单张图片
    # source = "path/to/your/image.jpg"

    # 3. 本地图片文件夹（批量处理）
    # source = "path/to/your/images/"

    # 4. 您的验证集图片文件夹
    source = "/home/<USER>/llm_project/yolo_project/datasets/大坦沙/好氧池面积/project-14-at-2025-08-15-08-36-dff0f5af/val/images/"

    # 5. 视频文件
    # source = "path/to/your/video.mp4"

    # 6. 摄像头（实时预测）
    # source = 0

    # =================================================================
    # 🎛️ 预测参数设置（可根据需要调整）
    # =================================================================

    conf_threshold = 0.4    # 置信度阈值 (0.0-1.0)
    iou_threshold = 0.5      # IoU阈值 (0.0-1.0)
    image_size = 640         # 推理图像大小
    line_width = 2           # 边界框线条宽度
    show_labels = True       # 显示类别标签
    show_confidence = True   # 显示置信度分数

    # =================================================================
    # 🚀 开始预测
    # =================================================================

    try:
        logger.info("=" * 60)
        logger.info("🌊 水体分割预测系统")
        logger.info("=" * 60)
        logger.info(f"📂 输入源: {source}")
        logger.info(f"🎯 置信度阈值: {conf_threshold}")
        logger.info(f"📏 图像大小: {image_size}")
        logger.info("=" * 60)

        # 执行预测
        predictor.predict(
            source=source,
            conf=conf_threshold,
            iou=iou_threshold,
            imgsz=image_size,
            show_labels=show_labels,
            show_conf=show_confidence,
            line_width=line_width,
            save_plots=True
        )

        logger.info("=" * 60)
        logger.info("✅ 预测完成！")
        logger.info("📁 结果已保存到: predict_result_seg/prediction/")
        logger.info("💡 提示: 修改 main() 函数中的 source 变量来预测不同的输入")
        logger.info("=" * 60)

    except Exception as e:
        logger.error("=" * 60)
        logger.error("❌ 预测失败！")
        logger.error(f"错误信息: {e}")
        logger.error("💡 请检查:")
        logger.error("   1. 模型文件是否存在")
        logger.error("   2. 输入源路径是否正确")
        logger.error("   3. 是否已激活正确的conda环境")
        logger.error("=" * 60)

if __name__ == "__main__":
    main()