#!/usr/bin/env python3
"""
简单的预测测试脚本
用于验证水体分割预测功能是否正常工作
"""

import os
import sys
from pathlib import Path

def testPrediction():
    """测试预测功能"""
    try:
        # 导入预测器
        from predict_seg import WaterSegmentationPredictor
        
        print("=" * 60)
        print("水体分割预测测试")
        print("=" * 60)
        
        # 检查模型文件是否存在
        model_path = "runs/segment/water_segmentation1/weights/best.pt"
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            print("请确保您已经训练了模型并且路径正确")
            return False
        
        print(f"✅ 找到模型文件: {model_path}")
        
        # 创建预测器
        print("\n📦 正在创建预测器...")
        predictor = WaterSegmentationPredictor(
            model_path=model_path,
            output_dir="predict_result_seg"
        )
        print("✅ 预测器创建成功")
        
        # 测试网络图片预测
        print("\n🌐 正在测试网络图片预测...")
        test_url = "https://ultralytics.com/images/bus.jpg"
        
        try:
            results = predictor.predict(
                source=test_url,
                conf=0.25,
                imgsz=640,
                show_labels=True,
                show_conf=True,
                line_width=2,
                save_plots=True
            )
            
            print("✅ 网络图片预测成功")
            print(f"📁 结果已保存到: predict_result_seg/prediction/")
            
            # 检查输出文件
            output_dir = Path("predict_result_seg/prediction")
            if output_dir.exists():
                output_files = list(output_dir.glob("*_segmented.*"))
                if output_files:
                    print(f"📸 生成的预测图像:")
                    for file in output_files:
                        print(f"   - {file.name}")
                else:
                    print("⚠️  未找到预测结果图像")
            
            return True
            
        except Exception as e:
            print(f"❌ 网络图片预测失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需的依赖包:")
        print("  - ultralytics")
        print("  - opencv-python")
        print("  - torch")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def checkEnvironment():
    """检查环境依赖"""
    print("🔍 检查环境依赖...")
    
    required_packages = [
        "ultralytics",
        "cv2",
        "torch",
        "PIL"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "cv2":
                import cv2
            elif package == "PIL":
                from PIL import Image
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请安装缺少的包:")
        if "cv2" in missing_packages:
            print("  pip install opencv-python")
        if "ultralytics" in missing_packages:
            print("  pip install ultralytics")
        if "torch" in missing_packages:
            print("  pip install torch")
        if "PIL" in missing_packages:
            print("  pip install pillow")
        return False
    
    print("✅ 所有依赖包都已安装")
    return True

def main():
    """主函数"""
    print("🚀 开始水体分割预测测试\n")
    
    # 检查环境
    if not checkEnvironment():
        print("\n❌ 环境检查失败，请安装缺少的依赖包")
        return
    
    print("\n" + "=" * 60)
    
    # 测试预测功能
    if testPrediction():
        print("\n🎉 测试成功完成！")
        print("\n📋 测试总结:")
        print("  ✅ 模型加载正常")
        print("  ✅ 预测功能正常")
        print("  ✅ 结果保存正常")
        print("\n💡 您现在可以使用以下方式进行预测:")
        print("  1. 运行 python predict-seg.py")
        print("  2. 运行 python predict_example.py")
        print("  3. 在代码中导入 WaterSegmentationPredictor 类")
    else:
        print("\n❌ 测试失败，请检查错误信息并解决问题")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
