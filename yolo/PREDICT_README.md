# 水体分割预测系统使用说明

## 概述

这个预测系统基于您训练好的 YOLO 分割模型，可以对各种输入源进行水体分割预测，并将结果保存到 `predict_result_seg` 文件夹中。

## 文件结构

```
yolo/
├── predict-seg.py          # 主要的预测类和功能
├── predict_example.py      # 使用示例脚本
├── PREDICT_README.md       # 本说明文件
└── predict_result_seg/     # 预测结果保存目录（自动创建）
    └── prediction/         # 具体的预测结果
        ├── *.jpg          # 带标注的预测图像
        ├── labels/        # 标注文件（txt格式）
        └── crops/         # 裁剪的检测区域（可选）
```

## 功能特性

### 支持的输入源
- **单张图片**: 本地图片文件或网络URL
- **图片文件夹**: 批量处理文件夹中的所有图片
- **视频文件**: 对视频进行逐帧分割
- **实时摄像头**: 实时分割摄像头画面
- **网络流**: 支持RTSP、RTMP等网络视频流

### 输出结果
- **可视化图像**: 带有分割掩码、边界框、标签和置信度的完整标注图像
- **直接绘制**: 分割结果直接绘制在原图上，无需单独的标注文件
- **高质量输出**: 保持原图分辨率，清晰显示分割边界
- **自定义样式**: 可调整线条宽度、颜色、标签显示等

## 快速开始

### 1. 基本使用

```python
from predict_seg import WaterSegmentationPredictor

# 创建预测器
predictor = WaterSegmentationPredictor()

# 预测单张图片
results = predictor.predict("path/to/your/image.jpg")
```

### 2. 运行示例脚本

```bash
# 运行预设的示例
python predict_example.py

# 或者直接运行主预测脚本
python predict-seg.py
```

### 3. 自定义预测

```python
from predict_seg import WaterSegmentationPredictor

# 创建预测器，指定模型路径和输出目录
predictor = WaterSegmentationPredictor(
    model_path="runs/segment/water_segmentation1/weights/best.pt",
    output_dir="my_predictions"
)

# 自定义预测参数
results = predictor.predict(
    source="your_input_source",
    conf=0.3,           # 置信度阈值
    iou=0.7,            # IoU阈值
    imgsz=640,          # 图像大小
    show_labels=True,   # 显示标签
    show_conf=True,     # 显示置信度
    line_width=3,       # 线条宽度
    save_plots=True     # 保存绘制结果
)
```

## 详细参数说明

### 预测参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `source` | str | 必需 | 输入源路径或URL |
| `conf` | float | 0.25 | 置信度阈值 (0.0-1.0) |
| `iou` | float | 0.7 | IoU阈值用于NMS (0.0-1.0) |
| `imgsz` | int | 640 | 推理图像大小 |
| `show_labels` | bool | True | 在图像上显示类别标签 |
| `show_conf` | bool | True | 在图像上显示置信度分数 |
| `line_width` | int | None | 边界框和掩码线条宽度，None为自动 |
| `save_plots` | bool | True | 是否保存绘制的结果图像 |

### 输入源格式

```python
# 本地图片
predictor.predict("image.jpg")

# 网络图片
predictor.predict("https://example.com/image.jpg")

# 图片文件夹
predictor.predict("path/to/images/")

# 视频文件
predictor.predict("video.mp4")

# 摄像头（设备索引）
predictor.predict(0)

# 网络流
predictor.predict("rtsp://example.com/stream")
```

## 输出结果说明

### 文件结构
```
predict_result_seg/prediction/
├── image1_segmented.jpg    # 带分割结果的图像
├── image2_segmented.jpg    # 分割掩码直接绘制在图像上
├── bus_segmented.jpg       # 包含边界框、掩码、标签、置信度
└── ...
```

### 输出图像特点
- **完整标注**: 每张图像包含分割掩码、边界框、类别标签和置信度分数
- **高质量渲染**: 保持原图分辨率和清晰度
- **颜色区分**: 不同类别使用不同颜色的掩码和边界框
- **信息丰富**: 标签显示类别名称和置信度百分比

## 常见使用场景

### 1. 批量处理图片
```python
predictor = WaterSegmentationPredictor()
results = predictor.predict("path/to/image_folder/")
```

### 2. 视频分析
```python
predictor = WaterSegmentationPredictor()
results = predictor.predict("water_video.mp4", conf=0.3)
```

### 3. 实时监控
```python
predictor = WaterSegmentationPredictor()
results = predictor.predict(0, conf=0.4)  # 摄像头实时预测
```

### 4. 高精度预测
```python
predictor = WaterSegmentationPredictor()
results = predictor.predict(
    "high_res_image.jpg",
    conf=0.5,        # 更高的置信度阈值
    imgsz=1280,      # 更大的图像尺寸
    line_width=4,    # 更粗的线条以适应高分辨率
    save_plots=True  # 保存高质量结果
)
```

## 注意事项

1. **模型路径**: 确保模型文件 `runs/segment/water_segmentation1/weights/best.pt` 存在
2. **输出目录**: 系统会自动创建 `predict_result_seg` 目录
3. **内存使用**: 处理大型视频或高分辨率图像时注意内存使用
4. **摄像头权限**: 使用摄像头时确保有相应的设备权限
5. **网络连接**: 处理网络资源时确保网络连接稳定

## 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   FileNotFoundError: 模型文件不存在
   ```
   解决方案: 检查模型路径是否正确

2. **输入文件不存在**
   ```
   输入源不存在或无法访问
   ```
   解决方案: 检查输入文件路径和权限

3. **内存不足**
   ```
   CUDA out of memory
   ```
   解决方案: 减小 `imgsz` 参数或使用CPU推理

4. **摄像头无法访问**
   ```
   无法打开摄像头设备
   ```
   解决方案: 检查摄像头连接和权限设置

## 性能优化建议

1. **GPU加速**: 确保CUDA环境正确配置
2. **批量处理**: 对多个图片使用文件夹输入而非逐个处理
3. **图像尺寸**: 根据需求调整 `imgsz` 参数平衡速度和精度
4. **置信度阈值**: 适当调整 `conf` 参数过滤低质量检测

## 联系支持

如有问题或建议，请查看日志输出获取详细错误信息。
